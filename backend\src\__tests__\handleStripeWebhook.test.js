// Mock AWS SDK before importing handler
const mockDynamoSend = jest.fn()

jest.mock('@aws-sdk/client-dynamodb', () => ({
  DynamoDBClient: jest.fn().mockImplementation(() => ({
    send: mockDynamoSend
  }))
}))

jest.mock('@aws-sdk/lib-dynamodb', () => ({
  DynamoDBDocumentClient: {
    from: jest.fn(() => ({
      send: mockDynamoSend
    }))
  },
  PutCommand: jest.fn(),
  UpdateCommand: jest.fn(),
  GetCommand: jest.fn()
}))

const { handler } = require('../handlers/handleStripeWebhook')

describe('handleStripeWebhook Lambda Handler', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    
    // Mock environment variables
    process.env.NODE_ENV = 'development'
    process.env.STRIPE_WEBHOOK_SECRET = 'whsec_placeholder'
    process.env.SUBSCRIPTIONS_TABLE_NAME = 'test-subscriptions-table'
    process.env.TRANSACTIONS_TABLE_NAME = 'test-transactions-table'
    process.env.AWS_REGION = 'us-east-1'
  })

  const createMockEvent = (eventType = 'customer.subscription.created', headers = {}) => ({
    headers: {
      'stripe-signature': 'mock_signature',
      ...headers
    },
    body: JSON.stringify({
      id: 'evt_test_123',
      type: eventType,
      data: {
        object: {
          id: 'sub_test_123',
          customer: 'cus_test_123',
          status: 'active',
          current_period_start: Math.floor(Date.now() / 1000),
          current_period_end: Math.floor(Date.now() / 1000) + (30 * 24 * 60 * 60),
          metadata: {
            userId: 'user-123'
          }
        }
      }
    })
  })

  describe('Success Cases', () => {
    test('should handle subscription created event (mock behavior)', async () => {
      mockDynamoSend
        .mockResolvedValueOnce({}) // PutCommand for subscription
        .mockResolvedValueOnce({}) // PutCommand for transaction

      const event = createMockEvent('customer.subscription.created')
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(200)
      expect(response.received).toBe(true)
      expect(response.eventType).toBe('customer.subscription.created')
      expect(response.eventId).toMatch(/^evt_mock_/)
      expect(mockDynamoSend).toHaveBeenCalledTimes(2) // subscription + transaction
    })

    test('should handle any event type in development (mock always returns subscription.created)', async () => {
      // In development mode, mock Stripe always returns subscription.created event
      // This tests that the handler processes the mock event correctly regardless of input
      mockDynamoSend
        .mockResolvedValueOnce({}) // PutCommand for subscription
        .mockResolvedValueOnce({}) // PutCommand for transaction

      const event = createMockEvent('customer.subscription.updated') // Input doesn't matter in dev mode
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(200)
      expect(response.received).toBe(true)
      expect(response.eventType).toBe('customer.subscription.created') // Mock always returns this
      expect(mockDynamoSend).toHaveBeenCalledTimes(2) // subscription + transaction
    })

    test('should handle webhook processing with mock Stripe', async () => {
      // Test that the webhook processing works end-to-end with mock Stripe
      mockDynamoSend
        .mockResolvedValueOnce({}) // PutCommand for subscription
        .mockResolvedValueOnce({}) // PutCommand for transaction

      const event = createMockEvent('any.event.type') // Type doesn't matter in dev mode
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(200)
      expect(response.received).toBe(true)
      expect(response.eventType).toBe('customer.subscription.created')
      expect(mockDynamoSend).toHaveBeenCalledTimes(2)
    })

    test('should handle webhook with different signature header case', async () => {
      // Test case-sensitive header handling
      mockDynamoSend
        .mockResolvedValueOnce({}) // PutCommand for subscription
        .mockResolvedValueOnce({}) // PutCommand for transaction

      const event = {
        headers: {
          'Stripe-Signature': 'mock_signature' // Capital S
        },
        body: JSON.stringify({
          id: 'evt_test_123',
          type: 'any.event.type'
        })
      }

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(200)
      expect(response.received).toBe(true)
      expect(response.eventType).toBe('customer.subscription.created') // Mock behavior
      expect(mockDynamoSend).toHaveBeenCalledTimes(2)
    })

    test('should handle webhook with missing signature header', async () => {
      // Test missing signature header (should still work with mock)
      mockDynamoSend
        .mockResolvedValueOnce({}) // PutCommand for subscription
        .mockResolvedValueOnce({}) // PutCommand for transaction

      const event = {
        headers: {},
        body: JSON.stringify({
          id: 'evt_test_123',
          type: 'any.event.type'
        })
      }

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(200)
      expect(response.received).toBe(true)
      expect(response.eventType).toBe('customer.subscription.created') // Mock behavior
      expect(mockDynamoSend).toHaveBeenCalledTimes(2)
    })

    test('should handle webhook with valid JSON body', async () => {
      // Test that the handler processes valid JSON correctly
      mockDynamoSend
        .mockResolvedValueOnce({}) // PutCommand for subscription
        .mockResolvedValueOnce({}) // PutCommand for transaction

      const event = {
        headers: {
          'stripe-signature': 'mock_signature'
        },
        body: JSON.stringify({
          id: 'evt_test_123',
          type: 'customer.subscription.created',
          data: {
            object: {
              id: 'sub_test_123'
            }
          }
        })
      }

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(200)
      expect(response.received).toBe(true)
      expect(response.eventType).toBe('customer.subscription.created')
      expect(mockDynamoSend).toHaveBeenCalledTimes(2)
    })
  })

  describe('Error Handling', () => {
    test('should handle webhook signature verification errors', async () => {
      // Test with production environment to trigger real Stripe verification
      process.env.NODE_ENV = 'production'
      process.env.STRIPE_WEBHOOK_SECRET = 'whsec_real_secret'

      const event = createMockEvent()
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(400)
      expect(response.error).toBe('Webhook processing failed')
    })

    test('should handle DynamoDB errors', async () => {
      mockDynamoSend.mockRejectedValue(new Error('DynamoDB error'))

      const event = createMockEvent('customer.subscription.created')
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(400)
      expect(response.error).toBe('Webhook processing failed')
    })

    test('should handle malformed event body', async () => {
      const event = {
        headers: {
          'stripe-signature': 'mock_signature'
        },
        body: 'invalid-json'
      }

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(400)
      expect(response.error).toBe('Webhook processing failed')
    })

    test('should handle environment variable edge cases', async () => {
      // Test with missing webhook secret (should use mock)
      delete process.env.STRIPE_WEBHOOK_SECRET

      mockDynamoSend
        .mockResolvedValueOnce({}) // PutCommand for subscription
        .mockResolvedValueOnce({}) // PutCommand for transaction

      const event = createMockEvent('customer.subscription.created')
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(200)
      expect(response.received).toBe(true)
    })

    test('should handle webhook processing flow', async () => {
      // Test the complete webhook processing flow
      mockDynamoSend
        .mockResolvedValueOnce({}) // PutCommand for subscription
        .mockResolvedValueOnce({}) // PutCommand for transaction

      const event = {
        headers: {
          'stripe-signature': 'test_signature'
        },
        body: JSON.stringify({
          id: 'evt_123',
          type: 'test.event'
        })
      }

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(200)
      expect(response.received).toBe(true)
      expect(response.eventId).toMatch(/^evt_mock_/)
    })
  })
})
