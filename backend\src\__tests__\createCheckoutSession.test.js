const { handler } = require('../handlers/createCheckoutSession')

describe('createCheckoutSession Lambda Handler', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    
    // Mock environment variables
    process.env.NODE_ENV = 'development'
    process.env.STRIPE_SECRET_KEY = 'sk_test_placeholder'
  })

  const createMockEvent = (body = {}, userId = 'test-user-123', email = '<EMAIL>') => ({
    requestContext: {
      authorizer: {
        claims: {
          sub: userId,
          email: email
        }
      }
    },
    body: JSON.stringify(body)
  })

  describe('Success Cases', () => {
    test('should create checkout session successfully with mock Stripe', async () => {
      const event = createMockEvent({
        planId: 'tunami-supporter',
        successUrl: 'https://example.com/success',
        cancelUrl: 'https://example.com/cancel'
      })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(200)
      expect(response.success).toBe(true)
      expect(response.data).toMatchObject({
        sessionId: expect.stringMatching(/^cs_mock_/),
        checkoutUrl: 'https://checkout.stripe.com/mock-session-url',
        customerId: 'cus_mock_customer',
        subscriptionId: expect.stringMatching(/^sub_mock_/)
      })
      expect(response.message).toBe('Checkout session created successfully')
    })

    test('should handle user without email', async () => {
      const event = createMockEvent({
        planId: 'tunami-supporter',
        successUrl: 'https://example.com/success',
        cancelUrl: 'https://example.com/cancel'
      }, 'test-user-123', undefined)

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(200)
      expect(response.success).toBe(true)
    })

    test('should use local-user as fallback when no userId', async () => {
      const event = {
        requestContext: {},
        body: JSON.stringify({
          planId: 'tunami-supporter',
          successUrl: 'https://example.com/success',
          cancelUrl: 'https://example.com/cancel'
        })
      }

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(200)
      expect(response.success).toBe(true)
    })
  })

  describe('Validation Errors', () => {
    test('should reject missing planId', async () => {
      const event = createMockEvent({
        successUrl: 'https://example.com/success',
        cancelUrl: 'https://example.com/cancel'
      })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(400)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('MISSING_PLAN_ID')
    })

    test('should reject missing successUrl', async () => {
      const event = createMockEvent({
        planId: 'tunami-supporter',
        cancelUrl: 'https://example.com/cancel'
      })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(400)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('MISSING_URLS')
    })

    test('should reject missing cancelUrl', async () => {
      const event = createMockEvent({
        planId: 'tunami-supporter',
        successUrl: 'https://example.com/success'
      })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(400)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('MISSING_URLS')
    })

    test('should reject invalid planId', async () => {
      const event = createMockEvent({
        planId: 'invalid-plan',
        successUrl: 'https://example.com/success',
        cancelUrl: 'https://example.com/cancel'
      })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(400)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('INVALID_PLAN')
    })

    test('should handle malformed JSON body', async () => {
      const event = {
        requestContext: {
          authorizer: {
            claims: {
              sub: 'test-user-123'
            }
          }
        },
        body: 'invalid-json'
      }

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(500)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('CHECKOUT_ERROR')
    })

    test('should handle missing body', async () => {
      const event = {
        requestContext: {
          authorizer: {
            claims: {
              sub: 'test-user-123'
            }
          }
        }
      }

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(400)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('MISSING_PLAN_ID')
    })
  })

  describe('Error Handling', () => {
    test('should handle mock Stripe errors by testing error paths', async () => {
      // Since we're using mock Stripe, we can test the error handling logic
      // by temporarily modifying the mock to throw errors
      const originalConsoleError = console.error
      console.error = jest.fn()

      // Test with an invalid environment that would cause the handler to fail
      const originalEnv = process.env.NODE_ENV
      process.env.NODE_ENV = 'production'
      process.env.STRIPE_SECRET_KEY = 'sk_test_real_key'

      const event = createMockEvent({
        planId: 'tunami-supporter',
        successUrl: 'https://example.com/success',
        cancelUrl: 'https://example.com/cancel'
      })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      // Should handle the error gracefully
      expect(result.statusCode).toBe(500)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('CHECKOUT_ERROR')

      // Restore environment
      process.env.NODE_ENV = originalEnv
      console.error = originalConsoleError
    })

    test('should handle environment detection correctly', async () => {
      // Test development environment detection
      process.env.NODE_ENV = 'development'
      process.env.STRIPE_SECRET_KEY = 'sk_test_placeholder'

      const event = createMockEvent({
        planId: 'tunami-supporter',
        successUrl: 'https://example.com/success',
        cancelUrl: 'https://example.com/cancel'
      })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(200)
      expect(response.success).toBe(true)
    })

    test('should handle missing Stripe key', async () => {
      // Test with missing Stripe key
      process.env.NODE_ENV = 'development'
      delete process.env.STRIPE_SECRET_KEY

      const event = createMockEvent({
        planId: 'tunami-supporter',
        successUrl: 'https://example.com/success',
        cancelUrl: 'https://example.com/cancel'
      })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(200)
      expect(response.success).toBe(true)
    })
  })
})
