// Mock AWS SDK before importing handler
const mockDynamoSend = jest.fn()

jest.mock('@aws-sdk/client-dynamodb', () => ({
  DynamoDBClient: jest.fn().mockImplementation(() => ({
    send: mockDynamoSend
  }))
}))

jest.mock('@aws-sdk/lib-dynamodb', () => ({
  DynamoDBDocumentClient: {
    from: jest.fn(() => ({
      send: mockDynamoSend
    }))
  },
  GetCommand: jest.fn()
}))

const { handler } = require('../handlers/createTippingPaymentIntent')

describe('createTippingPaymentIntent Lambda Handler', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    
    // Mock environment variables
    process.env.NODE_ENV = 'development'
    process.env.STRIPE_SECRET_KEY = 'sk_test_placeholder'
    process.env.USERS_TABLE_NAME = 'test-users-table'
    process.env.TRACKS_TABLE_NAME = 'test-tracks-table'
    process.env.AWS_REGION = 'us-east-1'
  })

  const createMockEvent = (body = {}, userId = 'sender-123', email = '<EMAIL>') => ({
    requestContext: {
      authorizer: {
        claims: {
          sub: userId,
          email: email
        }
      }
    },
    body: JSON.stringify(body)
  })

  const mockUser = (userId, username) => ({
    userId,
    username,
    email: `${username}@example.com`
  })

  const mockTrack = (trackId, creatorId, title = 'Test Track') => ({
    trackId,
    creatorId,
    title,
    genre: 'Electronic'
  })

  describe('Success Cases', () => {
    test('should create tipping payment intent successfully', async () => {
      // Mock DynamoDB responses
      mockDynamoSend
        .mockResolvedValueOnce({ Item: mockUser('recipient-123', 'recipient') }) // getUser(toUserId)
        .mockResolvedValueOnce({ Item: mockTrack('track-123', 'recipient-123') }) // getTrack(trackId)
        .mockResolvedValueOnce({ Item: mockUser('sender-123', 'sender') }) // getUser(fromUserId)

      const event = createMockEvent({
        toUserId: 'recipient-123',
        trackId: 'track-123',
        amount: 500, // $5.00
        currency: 'usd',
        message: 'Great track!'
      })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(200)
      expect(response.success).toBe(true)
      expect(response.data).toMatchObject({
        paymentIntentId: expect.stringMatching(/^pi_mock_/),
        clientSecret: expect.stringMatching(/^pi_mock_.*_secret_mock$/),
        amount: 500,
        currency: 'usd',
        platformFee: 50, // 10% of 500
        creatorAmount: 450, // 500 - 50
        recipient: {
          userId: 'recipient-123',
          username: 'recipient'
        },
        track: {
          trackId: 'track-123',
          title: 'Test Track'
        }
      })
      expect(response.message).toBe('Payment intent created successfully')
      expect(mockDynamoSend).toHaveBeenCalledTimes(3)
    })

    test('should handle minimum tip amount', async () => {
      mockDynamoSend
        .mockResolvedValueOnce({ Item: mockUser('recipient-123', 'recipient') })
        .mockResolvedValueOnce({ Item: mockTrack('track-123', 'recipient-123') })
        .mockResolvedValueOnce({ Item: mockUser('sender-123', 'sender') })

      const event = createMockEvent({
        toUserId: 'recipient-123',
        trackId: 'track-123',
        amount: 100 // $1.00 minimum
      })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(200)
      expect(response.data.amount).toBe(100)
      expect(response.data.platformFee).toBe(10)
      expect(response.data.creatorAmount).toBe(90)
    })

    test('should handle maximum tip amount', async () => {
      mockDynamoSend
        .mockResolvedValueOnce({ Item: mockUser('recipient-123', 'recipient') })
        .mockResolvedValueOnce({ Item: mockTrack('track-123', 'recipient-123') })
        .mockResolvedValueOnce({ Item: mockUser('sender-123', 'sender') })

      const event = createMockEvent({
        toUserId: 'recipient-123',
        trackId: 'track-123',
        amount: 10000 // $100.00 maximum
      })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(200)
      expect(response.data.amount).toBe(10000)
      expect(response.data.platformFee).toBe(1000)
      expect(response.data.creatorAmount).toBe(9000)
    })

    test('should handle empty message', async () => {
      mockDynamoSend
        .mockResolvedValueOnce({ Item: mockUser('recipient-123', 'recipient') })
        .mockResolvedValueOnce({ Item: mockTrack('track-123', 'recipient-123') })
        .mockResolvedValueOnce({ Item: mockUser('sender-123', 'sender') })

      const event = createMockEvent({
        toUserId: 'recipient-123',
        trackId: 'track-123',
        amount: 500,
        message: ''
      })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(200)
      expect(response.success).toBe(true)
    })

    test('should handle missing sender user info', async () => {
      mockDynamoSend
        .mockResolvedValueOnce({ Item: mockUser('recipient-123', 'recipient') })
        .mockResolvedValueOnce({ Item: mockTrack('track-123', 'recipient-123') })
        .mockResolvedValueOnce({ Item: null }) // sender user not found

      const event = createMockEvent({
        toUserId: 'recipient-123',
        trackId: 'track-123',
        amount: 500
      })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(200)
      expect(response.success).toBe(true)
    })
  })

  describe('Validation Errors', () => {
    test('should reject missing toUserId', async () => {
      const event = createMockEvent({
        trackId: 'track-123',
        amount: 500
      })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(400)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('VALIDATION_ERROR')
    })

    test('should reject missing trackId', async () => {
      const event = createMockEvent({
        toUserId: 'recipient-123',
        amount: 500
      })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(400)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('VALIDATION_ERROR')
    })

    test('should reject missing amount', async () => {
      const event = createMockEvent({
        toUserId: 'recipient-123',
        trackId: 'track-123'
      })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(400)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('VALIDATION_ERROR')
    })

    test('should reject amount below minimum', async () => {
      const event = createMockEvent({
        toUserId: 'recipient-123',
        trackId: 'track-123',
        amount: 50 // Below $1.00 minimum
      })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(400)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('VALIDATION_ERROR')
    })

    test('should reject amount above maximum', async () => {
      const event = createMockEvent({
        toUserId: 'recipient-123',
        trackId: 'track-123',
        amount: 15000 // Above $100.00 maximum
      })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(400)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('VALIDATION_ERROR')
    })

    test('should reject invalid currency', async () => {
      const event = createMockEvent({
        toUserId: 'recipient-123',
        trackId: 'track-123',
        amount: 500,
        currency: 'eur' // Only USD supported
      })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(400)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('VALIDATION_ERROR')
    })

    test('should reject message too long', async () => {
      const event = createMockEvent({
        toUserId: 'recipient-123',
        trackId: 'track-123',
        amount: 500,
        message: 'a'.repeat(501) // Over 500 character limit
      })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(400)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('VALIDATION_ERROR')
    })

    test('should reject self-tipping', async () => {
      const event = createMockEvent({
        toUserId: 'sender-123', // Same as sender
        trackId: 'track-123',
        amount: 500
      }, 'sender-123')

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(400)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('SELF_TIP_ERROR')
    })

    test('should handle malformed JSON body', async () => {
      const event = {
        requestContext: {
          authorizer: {
            claims: {
              sub: 'sender-123'
            }
          }
        },
        body: 'invalid-json'
      }

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(500)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('PAYMENT_INTENT_ERROR')
    })
  })

  describe('Business Logic Errors', () => {
    test('should handle recipient user not found', async () => {
      mockDynamoSend.mockResolvedValueOnce({ Item: null }) // recipient user not found

      const event = createMockEvent({
        toUserId: 'nonexistent-user',
        trackId: 'track-123',
        amount: 500
      })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(404)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('USER_NOT_FOUND')
    })

    test('should handle track not found', async () => {
      mockDynamoSend
        .mockResolvedValueOnce({ Item: mockUser('recipient-123', 'recipient') })
        .mockResolvedValueOnce({ Item: null }) // track not found

      const event = createMockEvent({
        toUserId: 'recipient-123',
        trackId: 'nonexistent-track',
        amount: 500
      })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(404)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('TRACK_NOT_FOUND')
    })

    test('should handle track owner mismatch', async () => {
      mockDynamoSend
        .mockResolvedValueOnce({ Item: mockUser('recipient-123', 'recipient') })
        .mockResolvedValueOnce({ Item: mockTrack('track-123', 'different-creator') }) // track belongs to different user

      const event = createMockEvent({
        toUserId: 'recipient-123',
        trackId: 'track-123',
        amount: 500
      })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(400)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('TRACK_OWNER_MISMATCH')
    })
  })

  describe('Database Errors', () => {
    test('should handle DynamoDB errors gracefully', async () => {
      // Since getUser and getTrack catch errors and return null,
      // database errors during user/track lookups are handled gracefully
      // The sender lookup error will be caught and the handler continues with null sender info
      mockDynamoSend
        .mockResolvedValueOnce({ Item: mockUser('recipient-123', 'recipient') }) // getUser(toUserId) succeeds
        .mockResolvedValueOnce({ Item: mockTrack('track-123', 'recipient-123') }) // getTrack(trackId) succeeds
        .mockResolvedValueOnce({ Item: null }) // getUser(fromUserId) returns null (simulating error handling)

      const event = createMockEvent({
        toUserId: 'recipient-123',
        trackId: 'track-123',
        amount: 500
      })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      // Should succeed even with database errors on non-critical operations
      expect(result.statusCode).toBe(200)
      expect(response.success).toBe(true)
    })
  })
})
