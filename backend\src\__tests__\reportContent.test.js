const AWS = require('aws-sdk');
const { handler } = require('../handlers/reportContent');

describe('reportContent Handler', () => {
  let mockDynamoDB;

  beforeEach(() => {
    // Setup DynamoDB mock
    mockDynamoDB = {
      query: jest.fn(),
      put: jest.fn()
    };
    AWS.DynamoDB.DocumentClient.mockImplementation(() => mockDynamoDB);

    // Mock Date.now() for consistent timestamps
    jest.spyOn(Date.prototype, 'toISOString').mockReturnValue('2023-12-01T10:00:00.000Z');
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Successful Content Reporting', () => {
    it('should successfully report track content', async () => {
      // Mock no existing report
      mockDynamoDB.query.mockReturnValue({
        promise: () => Promise.resolve({ Items: [] })
      });

      // Mock successful put
      mockDynamoDB.put.mockReturnValue({
        promise: () => Promise.resolve({})
      });

      const event = {
        body: JSON.stringify({
          contentId: 'track-123',
          contentType: 'track',
          reason: 'inappropriate',
          description: 'This track contains inappropriate content'
        }),
        requestContext: {
          authorizer: {
            claims: {
              sub: 'user-123'
            }
          }
        }
      };

      const result = await handler(event);

      expect(result.statusCode).toBe(201);
      const body = JSON.parse(result.body);
      expect(body.message).toBe('Content reported successfully');
      expect(body.reportId).toBe('test-uuid-1');
      expect(body.status).toBe('pending');

      // Verify DynamoDB operations
      expect(mockDynamoDB.query).toHaveBeenCalledWith({
        TableName: 'test-reports-table',
        IndexName: 'status-reportedAt-index',
        KeyConditionExpression: '#status = :status',
        FilterExpression: 'reporterId = :reporterId AND SK = :targetKey',
        ExpressionAttributeNames: {
          '#status': 'status'
        },
        ExpressionAttributeValues: {
          ':status': 'pending',
          ':reporterId': 'user-123',
          ':targetKey': 'TARGET#TRACK#track-123'
        }
      });

      expect(mockDynamoDB.put).toHaveBeenCalledWith({
        TableName: 'test-reports-table',
        Item: {
          PK: 'REPORT#test-uuid-1',
          SK: 'TARGET#TRACK#track-123',
          reportId: 'test-uuid-1',
          reporterId: 'user-123',
          contentId: 'track-123',
          contentType: 'track',
          reason: 'inappropriate',
          description: 'This track contains inappropriate content',
          status: 'pending',
          reportedAt: '2023-12-01T10:00:00.000Z',
          priority: 'medium',
          createdAt: '2023-12-01T10:00:00.000Z',
          updatedAt: '2023-12-01T10:00:00.000Z'
        }
      });
    });

    it('should successfully report comment content', async () => {
      mockDynamoDB.query.mockReturnValue({
        promise: () => Promise.resolve({ Items: [] })
      });

      mockDynamoDB.put.mockReturnValue({
        promise: () => Promise.resolve({})
      });

      const event = {
        body: JSON.stringify({
          contentId: 'comment-456',
          contentType: 'comment',
          reason: 'spam'
        }),
        requestContext: {
          authorizer: {
            claims: {
              sub: 'user-456'
            }
          }
        }
      };

      const result = await handler(event);

      expect(result.statusCode).toBe(201);
      expect(mockDynamoDB.put).toHaveBeenCalledWith({
        TableName: 'test-reports-table',
        Item: expect.objectContaining({
          contentType: 'comment',
          reason: 'spam',
          priority: 'low',
          description: ''
        })
      });
    });

    it('should assign correct priority levels', async () => {
      mockDynamoDB.query.mockReturnValue({
        promise: () => Promise.resolve({ Items: [] })
      });

      mockDynamoDB.put.mockReturnValue({
        promise: () => Promise.resolve({})
      });

      // Test high priority
      const highPriorityEvent = {
        body: JSON.stringify({
          contentId: 'track-123',
          contentType: 'track',
          reason: 'hate_speech'
        }),
        requestContext: {
          authorizer: {
            claims: { sub: 'user-123' }
          }
        }
      };

      await handler(highPriorityEvent);
      expect(mockDynamoDB.put).toHaveBeenCalledWith(
        expect.objectContaining({
          Item: expect.objectContaining({
            priority: 'high'
          })
        })
      );
    });
  });

  describe('Validation Errors', () => {
    it('should return 400 for missing required fields', async () => {
      const event = {
        body: JSON.stringify({
          contentId: 'track-123'
          // Missing contentType and reason
        }),
        requestContext: {
          authorizer: {
            claims: { sub: 'user-123' }
          }
        }
      };

      const result = await handler(event);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Missing required fields: contentId, contentType, reason');
    });

    it('should return 400 for invalid content type', async () => {
      const event = {
        body: JSON.stringify({
          contentId: 'track-123',
          contentType: 'invalid-type',
          reason: 'spam'
        }),
        requestContext: {
          authorizer: {
            claims: { sub: 'user-123' }
          }
        }
      };

      const result = await handler(event);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Invalid content type. Must be: track, comment');
    });

    it('should return 400 for invalid reason', async () => {
      const event = {
        body: JSON.stringify({
          contentId: 'track-123',
          contentType: 'track',
          reason: 'invalid-reason'
        }),
        requestContext: {
          authorizer: {
            claims: { sub: 'user-123' }
          }
        }
      };

      const result = await handler(event);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.error).toContain('Invalid reason. Must be one of:');
    });
  });

  describe('Duplicate Report Handling', () => {
    it('should return 409 for duplicate report', async () => {
      // Mock existing report found
      mockDynamoDB.query.mockReturnValue({
        promise: () => Promise.resolve({
          Items: [{ reportId: 'existing-report' }]
        })
      });

      const event = {
        body: JSON.stringify({
          contentId: 'track-123',
          contentType: 'track',
          reason: 'spam'
        }),
        requestContext: {
          authorizer: {
            claims: { sub: 'user-123' }
          }
        }
      };

      const result = await handler(event);

      expect(result.statusCode).toBe(409);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('You have already reported this content');
    });

    it('should continue with report creation if duplicate check fails', async () => {
      // Mock query error
      mockDynamoDB.query.mockReturnValue({
        promise: () => Promise.reject(new Error('Query failed'))
      });

      // Mock successful put
      mockDynamoDB.put.mockReturnValue({
        promise: () => Promise.resolve({})
      });

      const event = {
        body: JSON.stringify({
          contentId: 'track-123',
          contentType: 'track',
          reason: 'spam'
        }),
        requestContext: {
          authorizer: {
            claims: { sub: 'user-123' }
          }
        }
      };

      const result = await handler(event);

      expect(result.statusCode).toBe(201);
      expect(mockDynamoDB.put).toHaveBeenCalled();
    });
  });

  describe('Error Handling', () => {
    it('should handle JSON parse errors', async () => {
      const event = {
        body: 'invalid-json',
        requestContext: {
          authorizer: {
            claims: { sub: 'user-123' }
          }
        }
      };

      const result = await handler(event);

      expect(result.statusCode).toBe(500);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Internal server error');
    });

    it('should handle DynamoDB put errors', async () => {
      mockDynamoDB.query.mockReturnValue({
        promise: () => Promise.resolve({ Items: [] })
      });

      mockDynamoDB.put.mockReturnValue({
        promise: () => Promise.reject(new Error('DynamoDB error'))
      });

      const event = {
        body: JSON.stringify({
          contentId: 'track-123',
          contentType: 'track',
          reason: 'spam'
        }),
        requestContext: {
          authorizer: {
            claims: { sub: 'user-123' }
          }
        }
      };

      const result = await handler(event);

      expect(result.statusCode).toBe(500);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Internal server error');
    });
  });

  describe('Priority Calculation', () => {
    beforeEach(() => {
      mockDynamoDB.query.mockReturnValue({
        promise: () => Promise.resolve({ Items: [] })
      });
      mockDynamoDB.put.mockReturnValue({
        promise: () => Promise.resolve({})
      });
    });

    it('should assign high priority for serious violations', async () => {
      const highPriorityReasons = ['copyright', 'hate_speech', 'violence'];

      for (const reason of highPriorityReasons) {
        const event = {
          body: JSON.stringify({
            contentId: 'track-123',
            contentType: 'track',
            reason
          }),
          requestContext: {
            authorizer: {
              claims: { sub: 'user-123' }
            }
          }
        };

        await handler(event);
        expect(mockDynamoDB.put).toHaveBeenCalledWith(
          expect.objectContaining({
            Item: expect.objectContaining({
              priority: 'high'
            })
          })
        );
      }
    });

    it('should assign medium priority for moderate violations', async () => {
      const mediumPriorityReasons = ['inappropriate', 'misinformation'];

      for (const reason of mediumPriorityReasons) {
        const event = {
          body: JSON.stringify({
            contentId: 'track-123',
            contentType: 'track',
            reason
          }),
          requestContext: {
            authorizer: {
              claims: { sub: 'user-123' }
            }
          }
        };

        await handler(event);
        expect(mockDynamoDB.put).toHaveBeenCalledWith(
          expect.objectContaining({
            Item: expect.objectContaining({
              priority: 'medium'
            })
          })
        );
      }
    });

    it('should assign low priority for other violations', async () => {
      const event = {
        body: JSON.stringify({
          contentId: 'track-123',
          contentType: 'track',
          reason: 'spam'
        }),
        requestContext: {
          authorizer: {
            claims: { sub: 'user-123' }
          }
        }
      };

      await handler(event);
      expect(mockDynamoDB.put).toHaveBeenCalledWith(
        expect.objectContaining({
          Item: expect.objectContaining({
            priority: 'low'
          })
        })
      );
    });
  });
});
