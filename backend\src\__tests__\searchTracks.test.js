// Mock AWS SDK before importing handler
const mockDynamoSend = jest.fn()

jest.mock('@aws-sdk/client-dynamodb', () => ({
  DynamoDBClient: jest.fn().mockImplementation(() => ({
    send: mockDynamoSend
  })),
  ScanCommand: jest.fn()
}))

jest.mock('@aws-sdk/util-dynamodb', () => ({
  unmarshall: jest.fn((item) => {
    // Simple mock implementation that converts DynamoDB format to plain object
    const result = {}
    for (const [key, value] of Object.entries(item)) {
      if (value.S) result[key] = value.S
      else if (value.N) result[key] = parseInt(value.N)
      else if (value.L) result[key] = value.L.map(v => v.S || v.N || v)
      else result[key] = value
    }
    return result
  })
}))

const { handler } = require('../handlers/searchTracks')

describe('searchTracks Lambda Handler', () => {
  beforeEach(() => {
    jest.clearAllMocks()

    // Mock environment variables
    process.env.TRACKS_TABLE_NAME = 'test-tracks-table'
    process.env.AWS_REGION = 'us-east-1'
  })

  const createMockEvent = (queryParams = {}) => ({
    queryStringParameters: queryParams
  })

  const mockTrackItem = (overrides = {}) => ({
    trackId: { S: 'track-123' },
    title: { S: 'Test Electronic Track' },
    genre: { S: 'Electronic' },
    description: { S: 'A test electronic track' },
    aiToolsUsed: { L: [{ S: 'AIVA' }, { S: 'Amper' }] },
    audioFileUrl: { S: 'https://example.com/track.mp3' },
    coverImageUrl: { S: 'https://example.com/cover.jpg' },
    uploadDate: { S: '2024-01-15T10:00:00Z' },
    isPublic: { S: 'true' },
    tags: { L: [{ S: 'electronic' }, { S: 'ai' }] },
    listenCount: { N: '42' },
    likeCount: { N: '15' },
    commentCount: { N: '5' },
    creatorId: { S: 'creator-123' },
    ...overrides
  })

  describe('Success Cases', () => {
    test('should search tracks by query successfully', async () => {
      const mockResponse = {
        Items: [mockTrackItem()],
        LastEvaluatedKey: null
      }
      mockDynamoSend.mockResolvedValue(mockResponse)

      const event = createMockEvent({ query: 'electronic' })
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(200)
      expect(response.success).toBe(true)
      expect(response.data.tracks).toHaveLength(1)
      expect(response.data.tracks[0]).toMatchObject({
        trackId: 'track-123',
        title: 'Test Electronic Track',
        genre: 'Electronic'
      })
      expect(response.data.query).toBe('electronic')
      expect(mockDynamoSend).toHaveBeenCalledTimes(1)
    })

    test('should search tracks with genre filter', async () => {
      const mockResponse = {
        Items: [mockTrackItem()],
        LastEvaluatedKey: null
      }
      mockDynamoSend.mockResolvedValue(mockResponse)

      const event = createMockEvent({ 
        query: 'test',
        genre: 'Electronic'
      })
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(200)
      expect(response.data.filters.genre).toBe('Electronic')
      // Verify DynamoDB was called (the FilterExpression is set on the ScanCommand, not the send call)
      expect(mockDynamoSend).toHaveBeenCalledTimes(1)
    })

    test('should search tracks with AI tool filter', async () => {
      const mockResponse = {
        Items: [mockTrackItem()],
        LastEvaluatedKey: null
      }
      mockDynamoSend.mockResolvedValue(mockResponse)

      const event = createMockEvent({ 
        query: 'test',
        aiTool: 'AIVA'
      })
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(200)
      expect(response.data.filters.aiTool).toBe('AIVA')
      // Verify DynamoDB was called (the FilterExpression is set on the ScanCommand, not the send call)
      expect(mockDynamoSend).toHaveBeenCalledTimes(1)
    })

    test('should handle pagination', async () => {
      const lastEvaluatedKey = { trackId: { S: 'track-123' } }
      const mockResponse = {
        Items: [mockTrackItem()],
        LastEvaluatedKey: lastEvaluatedKey
      }
      mockDynamoSend.mockResolvedValue(mockResponse)

      const event = createMockEvent({ 
        query: 'test',
        limit: '10'
      })
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(200)
      expect(response.data.pagination.hasMore).toBe(true)
      expect(response.data.pagination.nextToken).toBeDefined()
      expect(response.data.pagination.limit).toBe(10)
    })

    test('should sort tracks by relevance score', async () => {
      const track1 = mockTrackItem({ 
        trackId: { S: 'track-1' },
        title: { S: 'Electronic Music' }, // Exact match
        likeCount: { N: '5' }
      })
      const track2 = mockTrackItem({ 
        trackId: { S: 'track-2' },
        title: { S: 'Some Electronic Track' }, // Contains match
        likeCount: { N: '20' }
      })
      
      const mockResponse = {
        Items: [track1, track2],
        LastEvaluatedKey: null
      }
      mockDynamoSend.mockResolvedValue(mockResponse)

      const event = createMockEvent({ query: 'Electronic Music' })
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(200)
      expect(response.data.tracks).toHaveLength(2)
      // track-1 should be first due to exact match (higher relevance score)
      expect(response.data.tracks[0].trackId).toBe('track-1')
    })

    test('should handle empty search results', async () => {
      const mockResponse = {
        Items: [],
        LastEvaluatedKey: null
      }
      mockDynamoSend.mockResolvedValue(mockResponse)

      const event = createMockEvent({ query: 'nonexistent' })
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(200)
      expect(response.data.tracks).toHaveLength(0)
      expect(response.data.totalCount).toBe(0)
    })
  })

  describe('Validation Errors', () => {
    test('should reject missing query parameter', async () => {
      const event = createMockEvent({})

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(400)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('VALIDATION_ERROR')
    })

    test('should reject empty query', async () => {
      const event = createMockEvent({ query: '' })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(400)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('VALIDATION_ERROR')
    })

    test('should reject invalid limit', async () => {
      const event = createMockEvent({ 
        query: 'test',
        limit: '100' // Over max limit of 50
      })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(400)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('VALIDATION_ERROR')
    })
  })

  describe('Error Handling', () => {
    test('should handle DynamoDB errors', async () => {
      mockDynamoSend.mockRejectedValue(new Error('DynamoDB error'))

      const event = createMockEvent({ query: 'test' })
      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(500)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('SEARCH_ERROR')
    })

    test('should handle invalid pagination token', async () => {
      const event = createMockEvent({ 
        query: 'test',
        lastEvaluatedKey: 'invalid-token'
      })

      const result = await handler(event)
      const response = JSON.parse(result.body)

      expect(result.statusCode).toBe(400)
      expect(response.success).toBe(false)
      expect(response.error.code).toBe('INVALID_PAGINATION')
    })
  })
})
