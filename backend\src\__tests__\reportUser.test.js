const AWS = require('aws-sdk');
const { handler } = require('../handlers/reportUser');

describe('reportUser Handler', () => {
  let mockDynamoDB;

  beforeEach(() => {
    // Setup DynamoDB mock
    mockDynamoDB = {
      query: jest.fn(),
      put: jest.fn()
    };
    AWS.DynamoDB.DocumentClient.mockImplementation(() => mockDynamoDB);

    // Mock Date.now() for consistent timestamps
    jest.spyOn(Date.prototype, 'toISOString').mockReturnValue('2023-12-01T10:00:00.000Z');
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Successful User Reporting', () => {
    it('should successfully report a user', async () => {
      // Mock no existing report
      mockDynamoDB.query.mockReturnValue({
        promise: () => Promise.resolve({ Items: [] })
      });

      // Mock successful put
      mockDynamoDB.put.mockReturnValue({
        promise: () => Promise.resolve({})
      });

      const event = {
        body: JSON.stringify({
          reportedUserId: 'reported-user-123',
          reason: 'harassment',
          description: 'This user is harassing other users'
        }),
        requestContext: {
          authorizer: {
            claims: {
              sub: 'reporter-user-456'
            }
          }
        }
      };

      const result = await handler(event);

      expect(result.statusCode).toBe(201);
      const body = JSON.parse(result.body);
      expect(body.message).toBe('User reported successfully');
      expect(body.reportId).toBe('test-uuid-1');
      expect(body.status).toBe('pending');

      // Verify DynamoDB operations
      expect(mockDynamoDB.query).toHaveBeenCalledWith({
        TableName: 'test-reports-table',
        IndexName: 'status-reportedAt-index',
        KeyConditionExpression: '#status = :status',
        FilterExpression: 'reporterId = :reporterId AND SK = :targetKey',
        ExpressionAttributeNames: {
          '#status': 'status'
        },
        ExpressionAttributeValues: {
          ':status': 'pending',
          ':reporterId': 'reporter-user-456',
          ':targetKey': 'TARGET#USER#reported-user-123'
        }
      });

      expect(mockDynamoDB.put).toHaveBeenCalledWith({
        TableName: 'test-reports-table',
        Item: {
          PK: 'REPORT#test-uuid-1',
          SK: 'TARGET#USER#reported-user-123',
          reportId: 'test-uuid-1',
          reporterId: 'reporter-user-456',
          reportedUserId: 'reported-user-123',
          contentType: 'user',
          reason: 'harassment',
          description: 'This user is harassing other users',
          status: 'pending',
          reportedAt: '2023-12-01T10:00:00.000Z',
          priority: 'high',
          createdAt: '2023-12-01T10:00:00.000Z',
          updatedAt: '2023-12-01T10:00:00.000Z'
        }
      });
    });

    it('should successfully report user without description', async () => {
      mockDynamoDB.query.mockReturnValue({
        promise: () => Promise.resolve({ Items: [] })
      });

      mockDynamoDB.put.mockReturnValue({
        promise: () => Promise.resolve({})
      });

      const event = {
        body: JSON.stringify({
          reportedUserId: 'reported-user-789',
          reason: 'spam'
        }),
        requestContext: {
          authorizer: {
            claims: {
              sub: 'reporter-user-456'
            }
          }
        }
      };

      const result = await handler(event);

      expect(result.statusCode).toBe(201);
      expect(mockDynamoDB.put).toHaveBeenCalledWith({
        TableName: 'test-reports-table',
        Item: expect.objectContaining({
          reason: 'spam',
          description: '',
          priority: 'low'
        })
      });
    });
  });

  describe('Validation Errors', () => {
    it('should return 400 for missing required fields', async () => {
      const event = {
        body: JSON.stringify({
          reportedUserId: 'user-123'
          // Missing reason
        }),
        requestContext: {
          authorizer: {
            claims: { sub: 'reporter-456' }
          }
        }
      };

      const result = await handler(event);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Missing required fields: reportedUserId, reason');
    });

    it('should return 400 for invalid reason', async () => {
      const event = {
        body: JSON.stringify({
          reportedUserId: 'user-123',
          reason: 'invalid-reason'
        }),
        requestContext: {
          authorizer: {
            claims: { sub: 'reporter-456' }
          }
        }
      };

      const result = await handler(event);

      expect(result.statusCode).toBe(400);
      const body = JSON.parse(result.body);
      expect(body.error).toContain('Invalid reason. Must be one of:');
    });
  });

  describe('Duplicate Report Handling', () => {
    it('should return 409 for duplicate report', async () => {
      // Mock existing report found
      mockDynamoDB.query.mockReturnValue({
        promise: () => Promise.resolve({
          Items: [{ reportId: 'existing-user-report' }]
        })
      });

      const event = {
        body: JSON.stringify({
          reportedUserId: 'user-123',
          reason: 'spam'
        }),
        requestContext: {
          authorizer: {
            claims: { sub: 'reporter-456' }
          }
        }
      };

      const result = await handler(event);

      expect(result.statusCode).toBe(409);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('You have already reported this user');
    });

    it('should continue with report creation if duplicate check fails', async () => {
      // Mock query error
      mockDynamoDB.query.mockReturnValue({
        promise: () => Promise.reject(new Error('Query failed'))
      });

      // Mock successful put
      mockDynamoDB.put.mockReturnValue({
        promise: () => Promise.resolve({})
      });

      const event = {
        body: JSON.stringify({
          reportedUserId: 'user-123',
          reason: 'spam'
        }),
        requestContext: {
          authorizer: {
            claims: { sub: 'reporter-456' }
          }
        }
      };

      const result = await handler(event);

      expect(result.statusCode).toBe(201);
      expect(mockDynamoDB.put).toHaveBeenCalled();
    });
  });

  describe('Error Handling', () => {
    it('should handle JSON parse errors', async () => {
      const event = {
        body: 'invalid-json',
        requestContext: {
          authorizer: {
            claims: { sub: 'reporter-456' }
          }
        }
      };

      const result = await handler(event);

      expect(result.statusCode).toBe(500);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Internal server error');
    });

    it('should handle DynamoDB put errors', async () => {
      mockDynamoDB.query.mockReturnValue({
        promise: () => Promise.resolve({ Items: [] })
      });

      mockDynamoDB.put.mockReturnValue({
        promise: () => Promise.reject(new Error('DynamoDB error'))
      });

      const event = {
        body: JSON.stringify({
          reportedUserId: 'user-123',
          reason: 'spam'
        }),
        requestContext: {
          authorizer: {
            claims: { sub: 'reporter-456' }
          }
        }
      };

      const result = await handler(event);

      expect(result.statusCode).toBe(500);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Internal server error');
    });
  });

  describe('Priority Calculation', () => {
    beforeEach(() => {
      mockDynamoDB.query.mockReturnValue({
        promise: () => Promise.resolve({ Items: [] })
      });
      mockDynamoDB.put.mockReturnValue({
        promise: () => Promise.resolve({})
      });
    });

    it('should assign high priority for serious violations', async () => {
      const highPriorityReasons = ['harassment', 'hate_speech', 'impersonation'];

      for (const reason of highPriorityReasons) {
        const event = {
          body: JSON.stringify({
            reportedUserId: 'user-123',
            reason
          }),
          requestContext: {
            authorizer: {
              claims: { sub: 'reporter-456' }
            }
          }
        };

        await handler(event);
        expect(mockDynamoDB.put).toHaveBeenCalledWith(
          expect.objectContaining({
            Item: expect.objectContaining({
              priority: 'high'
            })
          })
        );
      }
    });

    it('should assign medium priority for moderate violations', async () => {
      const mediumPriorityReasons = ['inappropriate_profile', 'fake_account'];

      for (const reason of mediumPriorityReasons) {
        const event = {
          body: JSON.stringify({
            reportedUserId: 'user-123',
            reason
          }),
          requestContext: {
            authorizer: {
              claims: { sub: 'reporter-456' }
            }
          }
        };

        await handler(event);
        expect(mockDynamoDB.put).toHaveBeenCalledWith(
          expect.objectContaining({
            Item: expect.objectContaining({
              priority: 'medium'
            })
          })
        );
      }
    });

    it('should assign low priority for other violations', async () => {
      const event = {
        body: JSON.stringify({
          reportedUserId: 'user-123',
          reason: 'spam'
        }),
        requestContext: {
          authorizer: {
            claims: { sub: 'reporter-456' }
          }
        }
      };

      await handler(event);
      expect(mockDynamoDB.put).toHaveBeenCalledWith(
        expect.objectContaining({
          Item: expect.objectContaining({
            priority: 'low'
          })
        })
      );
    });
  });

  describe('Valid Reason Categories', () => {
    beforeEach(() => {
      mockDynamoDB.query.mockReturnValue({
        promise: () => Promise.resolve({ Items: [] })
      });
      mockDynamoDB.put.mockReturnValue({
        promise: () => Promise.resolve({})
      });
    });

    it('should accept all valid user report categories', async () => {
      const validReasons = [
        'spam', 'harassment', 'impersonation',
        'inappropriate_profile', 'hate_speech', 'fake_account', 'other'
      ];

      for (const reason of validReasons) {
        const event = {
          body: JSON.stringify({
            reportedUserId: 'user-123',
            reason
          }),
          requestContext: {
            authorizer: {
              claims: { sub: 'reporter-456' }
            }
          }
        };

        const result = await handler(event);
        expect(result.statusCode).toBe(201);
      }
    });
  });
});
