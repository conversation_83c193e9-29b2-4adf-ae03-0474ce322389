// Create a virtual mock for aws-sdk
const mockDynamoDBDocumentClient = {
  query: jest.fn(),
  put: jest.fn(),
  get: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
  scan: jest.fn()
};

// Mock AWS SDK before requiring the handler
jest.mock('aws-sdk', () => ({
  DynamoDB: {
    DocumentClient: jest.fn(() => mockDynamoDBDocumentClient)
  }
}), { virtual: true });

const { handler } = require('../handlers/getReportsQueue');

describe('getReportsQueue Handler', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Admin Authorization', () => {
    it('should return 403 for non-admin users', async () => {
      const event = {
        requestContext: {
          authorizer: {
            claims: {
              'cognito:groups': ['RegularUsers']
            }
          }
        },
        queryStringParameters: {}
      };

      const result = await handler(event);

      expect(result.statusCode).toBe(403);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Access denied. Admin privileges required.');
    });

    it('should return 403 for users without groups', async () => {
      const event = {
        requestContext: {
          authorizer: {
            claims: {}
          }
        },
        queryStringParameters: {}
      };

      const result = await handler(event);

      expect(result.statusCode).toBe(403);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Access denied. Admin privileges required.');
    });

    it('should allow access for admin users', async () => {
      // Mock successful query
      mockDynamoDBDocumentClient.query.mockReturnValue({
        promise: () => Promise.resolve({
          Items: [],
          LastEvaluatedKey: null
        })
      });

      const event = {
        requestContext: {
          authorizer: {
            claims: {
              'cognito:groups': ['TunamiAdmins']
            }
          }
        },
        queryStringParameters: {}
      };

      const result = await handler(event);

      expect(result.statusCode).toBe(200);
    });
  });

  describe('Query Parameters Handling', () => {
    beforeEach(() => {
      mockDynamoDBDocumentClient.query.mockReturnValue({
        promise: () => Promise.resolve({
          Items: [],
          LastEvaluatedKey: null
        })
      });
    });

    it('should use default parameters when none provided', async () => {
      const event = {
        requestContext: {
          authorizer: {
            claims: {
              'cognito:groups': ['TunamiAdmins']
            }
          }
        },
        queryStringParameters: null
      };

      await handler(event);

      expect(mockDynamoDBDocumentClient.query).toHaveBeenCalledWith({
        TableName: 'test-reports-table',
        IndexName: 'status-reportedAt-index',
        KeyConditionExpression: '#status = :status',
        ExpressionAttributeNames: {
          '#status': 'status'
        },
        ExpressionAttributeValues: {
          ':status': 'pending'
        },
        ScanIndexForward: false,
        Limit: 50
      });
    });

    it('should handle custom status parameter', async () => {
      const event = {
        requestContext: {
          authorizer: {
            claims: {
              'cognito:groups': ['TunamiAdmins']
            }
          }
        },
        queryStringParameters: {
          status: 'resolved'
        }
      };

      await handler(event);

      expect(mockDynamoDBDocumentClient.query).toHaveBeenCalledWith(
        expect.objectContaining({
          ExpressionAttributeValues: {
            ':status': 'resolved'
          }
        })
      );
    });

    it('should handle priority filter', async () => {
      const event = {
        requestContext: {
          authorizer: {
            claims: {
              'cognito:groups': ['TunamiAdmins']
            }
          }
        },
        queryStringParameters: {
          priority: 'high'
        }
      };

      await handler(event);

      expect(mockDynamoDBDocumentClient.query).toHaveBeenCalledWith(
        expect.objectContaining({
          FilterExpression: 'priority = :priority',
          ExpressionAttributeValues: {
            ':status': 'pending',
            ':priority': 'high'
          }
        })
      );
    });

    it('should handle custom limit', async () => {
      const event = {
        requestContext: {
          authorizer: {
            claims: {
              'cognito:groups': ['TunamiAdmins']
            }
          }
        },
        queryStringParameters: {
          limit: '25'
        }
      };

      await handler(event);

      expect(mockDynamoDBDocumentClient.query).toHaveBeenCalledWith(
        expect.objectContaining({
          Limit: 25
        })
      );
    });

    it('should handle pagination with lastEvaluatedKey', async () => {
      const lastKey = { PK: 'REPORT#123', SK: 'TARGET#TRACK#456' };
      const event = {
        requestContext: {
          authorizer: {
            claims: {
              'cognito:groups': ['TunamiAdmins']
            }
          }
        },
        queryStringParameters: {
          lastEvaluatedKey: encodeURIComponent(JSON.stringify(lastKey))
        }
      };

      await handler(event);

      expect(mockDynamoDBDocumentClient.query).toHaveBeenCalledWith(
        expect.objectContaining({
          ExclusiveStartKey: lastKey
        })
      );
    });
  });

  describe('Report Enrichment', () => {
    it('should enrich reports with user and track information', async () => {
      const mockReports = [
        {
          reportId: 'report-1',
          reporterId: 'user-1',
          contentType: 'track',
          contentId: 'track-1',
          reason: 'spam'
        },
        {
          reportId: 'report-2',
          reporterId: 'user-2',
          contentType: 'user',
          reportedUserId: 'user-3',
          reason: 'harassment'
        }
      ];

      mockDynamoDBDocumentClient.query.mockReturnValue({
        promise: () => Promise.resolve({
          Items: mockReports,
          LastEvaluatedKey: null
        })
      });

      // Mock user info calls
      mockDynamoDBDocumentClient.get
        .mockReturnValueOnce({
          promise: () => Promise.resolve({
            Item: {
              userId: 'user-1',
              username: 'reporter1',
              email: '<EMAIL>'
            }
          })
        })
        .mockReturnValueOnce({
          promise: () => Promise.resolve({
            Item: {
              trackId: 'track-1',
              title: 'Test Track',
              artist: 'Test Artist'
            }
          })
        })
        .mockReturnValueOnce({
          promise: () => Promise.resolve({
            Item: {
              userId: 'user-2',
              username: 'reporter2',
              email: '<EMAIL>'
            }
          })
        })
        .mockReturnValueOnce({
          promise: () => Promise.resolve({
            Item: {
              userId: 'user-3',
              username: 'reported_user',
              email: '<EMAIL>'
            }
          })
        });

      const event = {
        requestContext: {
          authorizer: {
            claims: {
              'cognito:groups': ['TunamiAdmins']
            }
          }
        },
        queryStringParameters: {}
      };

      const result = await handler(event);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      
      expect(body.reports).toHaveLength(2);
      expect(body.reports[0]).toEqual(
        expect.objectContaining({
          reportId: 'report-1',
          reporter: {
            userId: 'user-1',
            username: 'reporter1',
            email: '<EMAIL>'
          },
          target: {
            trackId: 'track-1',
            title: 'Test Track',
            artist: 'Test Artist'
          }
        })
      );
    });

    it('should handle comment content type', async () => {
      const mockReports = [
        {
          reportId: 'report-1',
          reporterId: 'user-1',
          contentType: 'comment',
          contentId: 'comment-1',
          reason: 'inappropriate'
        }
      ];

      mockDynamoDBDocumentClient.query.mockReturnValue({
        promise: () => Promise.resolve({
          Items: mockReports,
          LastEvaluatedKey: null
        })
      });

      mockDynamoDBDocumentClient.get.mockReturnValue({
        promise: () => Promise.resolve({
          Item: {
            userId: 'user-1',
            username: 'reporter1'
          }
        })
      });

      const event = {
        requestContext: {
          authorizer: {
            claims: {
              'cognito:groups': ['TunamiAdmins']
            }
          }
        },
        queryStringParameters: {}
      };

      const result = await handler(event);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);
      
      expect(body.reports[0].target).toEqual({
        id: 'comment-1',
        type: 'comment'
      });
    });

    it('should handle enrichment errors gracefully', async () => {
      const mockReports = [
        {
          reportId: 'report-1',
          reporterId: 'user-1',
          contentType: 'track',
          contentId: 'track-1',
          reason: 'spam'
        }
      ];

      mockDynamoDB.query.mockReturnValue({
        promise: () => Promise.resolve({
          Items: mockReports,
          LastEvaluatedKey: null
        })
      });

      // Mock get calls to fail
      mockDynamoDB.get.mockReturnValue({
        promise: () => Promise.reject(new Error('DynamoDB error'))
      });

      const event = {
        requestContext: {
          authorizer: {
            claims: {
              'cognito:groups': ['TunamiAdmins']
            }
          }
        },
        queryStringParameters: {}
      };

      const result = await handler(event);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);

      // Should return original report when enrichment fails
      expect(body.reports[0]).toEqual(mockReports[0]);
    });

    it('should handle missing user/track data', async () => {
      const mockReports = [
        {
          reportId: 'report-1',
          reporterId: 'user-1',
          contentType: 'track',
          contentId: 'track-1',
          reason: 'spam'
        }
      ];

      mockDynamoDB.query.mockReturnValue({
        promise: () => Promise.resolve({
          Items: mockReports,
          LastEvaluatedKey: null
        })
      });

      // Mock get calls to return empty results
      mockDynamoDB.get.mockReturnValue({
        promise: () => Promise.resolve({ Item: null })
      });

      const event = {
        requestContext: {
          authorizer: {
            claims: {
              'cognito:groups': ['TunamiAdmins']
            }
          }
        },
        queryStringParameters: {}
      };

      const result = await handler(event);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);

      expect(body.reports[0].reporter).toEqual({
        userId: 'user-1',
        username: 'Unknown User'
      });
      expect(body.reports[0].target).toEqual({
        trackId: 'track-1',
        title: 'Unknown Track'
      });
    });
  });

  describe('Response Structure', () => {
    it('should return correct response structure', async () => {
      const mockReports = [
        {
          reportId: 'report-1',
          reporterId: 'user-1',
          contentType: 'track',
          contentId: 'track-1',
          reason: 'spam'
        }
      ];

      const lastEvaluatedKey = { PK: 'REPORT#123', SK: 'TARGET#TRACK#456' };

      mockDynamoDB.query.mockReturnValue({
        promise: () => Promise.resolve({
          Items: mockReports,
          LastEvaluatedKey: lastEvaluatedKey
        })
      });

      mockDynamoDB.get.mockReturnValue({
        promise: () => Promise.resolve({
          Item: {
            userId: 'user-1',
            username: 'testuser'
          }
        })
      });

      const event = {
        requestContext: {
          authorizer: {
            claims: {
              'cognito:groups': ['TunamiAdmins']
            }
          }
        },
        queryStringParameters: {
          status: 'pending',
          priority: 'high'
        }
      };

      const result = await handler(event);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);

      expect(body).toEqual({
        reports: expect.any(Array),
        pagination: {
          hasMore: true,
          lastEvaluatedKey: encodeURIComponent(JSON.stringify(lastEvaluatedKey)),
          count: 1
        },
        filters: {
          status: 'pending',
          priority: 'high'
        }
      });
    });

    it('should handle no more results', async () => {
      mockDynamoDB.query.mockReturnValue({
        promise: () => Promise.resolve({
          Items: [],
          LastEvaluatedKey: null
        })
      });

      const event = {
        requestContext: {
          authorizer: {
            claims: {
              'cognito:groups': ['TunamiAdmins']
            }
          }
        },
        queryStringParameters: {}
      };

      const result = await handler(event);

      expect(result.statusCode).toBe(200);
      const body = JSON.parse(result.body);

      expect(body.pagination).toEqual({
        hasMore: false,
        lastEvaluatedKey: null,
        count: 0
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle DynamoDB query errors', async () => {
      mockDynamoDB.query.mockReturnValue({
        promise: () => Promise.reject(new Error('DynamoDB error'))
      });

      const event = {
        requestContext: {
          authorizer: {
            claims: {
              'cognito:groups': ['TunamiAdmins']
            }
          }
        },
        queryStringParameters: {}
      };

      const result = await handler(event);

      expect(result.statusCode).toBe(500);
      const body = JSON.parse(result.body);
      expect(body.error).toBe('Internal server error');
    });

    it('should handle invalid lastEvaluatedKey JSON', async () => {
      mockDynamoDB.query.mockReturnValue({
        promise: () => Promise.resolve({
          Items: [],
          LastEvaluatedKey: null
        })
      });

      const event = {
        requestContext: {
          authorizer: {
            claims: {
              'cognito:groups': ['TunamiAdmins']
            }
          }
        },
        queryStringParameters: {
          lastEvaluatedKey: 'invalid-json'
        }
      };

      const result = await handler(event);

      // Should handle gracefully and not crash
      expect(result.statusCode).toBe(200);
    });
  });
});
